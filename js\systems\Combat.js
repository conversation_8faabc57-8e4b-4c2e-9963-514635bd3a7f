// ===== COMBAT SYSTEM =====
// Turn-based combat mechanics, AI, and battle management

class Combat {
    constructor() {
        this.enemies = [];
        this.turnOrder = [];
        this.currentTurnIndex = 0;
        this.combatLog = [];
        this.waitingForPlayerInput = false;
        this.enemyActionTimeout = null;
        this.currentSkillActor = null;
        this.currentSelectedSkill = null;
    }
    
    // Start a new battle
    startBattle(enemies) {
        this.enemies = Array.isArray(enemies) ? enemies : [enemies];
        this.combatLog = [];
        this.wildMeatObtained = false; // Reset wild meat flag for new battle
        this.waitingForPlayerInput = false; // Reset waiting state
        this.pendingEnemyAction = null; // Clear any pending actions

        // Reset all combat states for party members
        characterSystem.resetCombatStates();

        // Start combat music
        if (window.soundManager && soundManager.startCombatMusic) {
            soundManager.startCombatMusic().then(result => {
                if (result.success) {
                    console.log('Combat music started successfully');
                } else {
                    console.warn('Failed to start combat music:', result.error);
                }
            }).catch(error => {
                console.error('Error starting combat music:', error);
            });
        }

        this.setupTurnOrder();
        this.updateCombatUI();

        if (this.enemies.length === 1) {
            this.addToLog(`A ${this.enemies[0].name} appears!`, 'system');
        } else {
            this.addToLog(`${this.enemies.length} enemies appear!`, 'system');
            this.enemies.forEach(enemy => {
                this.addToLog(`${enemy.name} joins the battle!`, 'system');
            });
        }

        // Start the first turn
        if (this.turnOrder[this.currentTurnIndex].type === 'enemy') {
            // Small delay to let UI update first
            setTimeout(() => {
                this.enemyTurn();
            }, 100);
        }
    }
    
    // Setup turn order based on agility with randomization
    setupTurnOrder() {
        this.turnOrder = [];
        
        const calculateEffectiveAgl = (baseAgl) => {
            const variancePercent = CONSTANTS.COMBAT.RANDOM_VARIANCE_PERCENT / 100;
            const variation = Math.floor(baseAgl * (Math.random() * (variancePercent * 2) - variancePercent));
            const effectiveAgl = baseAgl + variation;
            return Math.max(CONSTANTS.COMBAT.MIN_RANDOM_VALUE, effectiveAgl);
        };
        
        // Add alive party members
        gameState.party.forEach(member => {
            if (member.life > 0) {
                // Apply swift movement effect to agility if active
                let effectiveBaseAgl = member.agl;
                if (member.swiftMovement && member.swiftMovementTurnsLeft > 0) {
                    effectiveBaseAgl = Math.floor(member.agl * CONSTANTS.SKILLS.LEVEL_20_SKILLS.SWIFT_MOVEMENT.agilityMultiplier);
                }

                this.turnOrder.push({
                    type: 'party',
                    member: member,
                    baseAgl: effectiveBaseAgl,
                    effectiveAgl: calculateEffectiveAgl(effectiveBaseAgl)
                });
            }
        });
        
        // Add all enemies
        this.enemies.forEach(enemy => {
            this.turnOrder.push({
                type: 'enemy',
                member: enemy,
                baseAgl: enemy.agl,
                effectiveAgl: calculateEffectiveAgl(enemy.agl)
            });
        });
        
        // Sort by effective agility (highest first)
        this.turnOrder.sort((a, b) => {
            if (b.effectiveAgl === a.effectiveAgl) {
                return b.baseAgl - a.baseAgl;
            }
            return b.effectiveAgl - a.effectiveAgl;
        });
        
        this.currentTurnIndex = 0;
    }
    
    // Calculate evasion chance based on agility difference
    calculateEvasionChance(targetAgl, attackerAgl) {
        const aglDifference = targetAgl - attackerAgl;
        let evasionChance = CONSTANTS.COMBAT.EVASION_BASE + (aglDifference * CONSTANTS.COMBAT.EVASION_FACTOR);
        return Math.max(0, Math.min(CONSTANTS.COMBAT.MAX_EVASION, evasionChance));
    }
    
    // Execute a combat action
    executeAction(actor, target, action) {
        actor.defending = false;
        
        switch(action) {
            case 'attack':
                // Check for SHIELD effect - redirect attack to shield bearer if active
                const shieldBearer = characterSystem.getShieldBearer();
                let actualTarget = target;

                if (shieldBearer && actor.type && target !== shieldBearer) {
                    // Enemy attacking, redirect to shield bearer
                    actualTarget = shieldBearer;
                    this.addToLog(`🛡️ ${shieldBearer.name}'s SHIELD intercepts the attack!`, 'system');
                }

                // Check for physical immunity (Will-o'-the-wisp and similar enemies)
                if (actualTarget.properties && actualTarget.properties.includes('nonmaterial')) {
                    this.addToLog(`💨 ${actualTarget.name} is immune to physical attacks! 💨`, 'system');
                    // Play evade sound effect for immunity
                    if (window.soundManager) {
                        soundManager.playEvade();
                    }
                    break;
                }

                // Check for physical attack nullify (soaring ability) - Dragon, Greater Demon, Dark Angel, etc.
                if (actualTarget.specialAbilities &&
                    actualTarget.specialAbilities.physicalAttackNullifyChance &&
                    Math.random() < actualTarget.specialAbilities.physicalAttackNullifyChance) {

                    // Different messages based on monster type
                    if (actualTarget.type === 'Dragon' || actualTarget.type === 'Red Dragon') {
                        this.addToLog(`🐉 ${actualTarget.name} soars into the air, nullifying the physical attack! 🐉`, 'system');
                    } else if (actualTarget.type === 'Greater Demon') {
                        this.addToLog(`👹 ${actualTarget.name} phases through the attack with otherworldly power! 👹`, 'system');
                    } else if (actualTarget.type === 'Dark Angel') {
                        this.addToLog(`😇 ${actualTarget.name} spreads divine wings, deflecting the attack! 😇`, 'system');
                    } else {
                        this.addToLog(`✨ ${actualTarget.name} nullifies the physical attack! ✨`, 'system');
                    }

                    // Play evade sound effect for nullification
                    if (window.soundManager) {
                        soundManager.playEvade();
                    }
                    break;
                }

                // Check for damage boost from concentration
                const hasBoost = actor.damageBoost;
                const boostMultiplier = hasBoost ? CONSTANTS.SKILLS.CONCENTRATE_DAMAGE_MULTIPLIER : 1;

                // Check for enemy miss chance (for enemies attacking players)
                if (actor.type && actor.specialAbilities && actor.specialAbilities.missChance) {
                    if (Math.random() < actor.specialAbilities.missChance) {
                        this.addToLog(`${actor.name}'s attack misses ${actualTarget.name || 'Enemy'}!`, 'system');
                        // Play evade sound effect for miss
                        if (window.soundManager) {
                            soundManager.playEvade();
                        }
                        break;
                    }
                }

                const attackEvasionChance = hasBoost ? 0 : this.calculateEvasionChance(actualTarget.agl, actor.agl);
                if (Math.random() < attackEvasionChance) {
                    this.addToLog(`${actualTarget.name || 'Enemy'} evades ${actor.name}'s attack!`, 'system');
                    // Play evade sound effect
                    if (window.soundManager) {
                        soundManager.playEvade();
                    }
                    break;
                }
                
                let attackDamage = Math.floor(actor.str * (0.8 + Math.random() * 0.4) * boostMultiplier);

                // Check for critical hit (Wing Lizard, Golem, Demon, Mire Stalker, etc.)
                let isCriticalHit = false;
                let criticalHitAgainstPlayer = false;
                if (actor.specialAbilities &&
                    actor.specialAbilities.criticalHitChance &&
                    Math.random() < actor.specialAbilities.criticalHitChance) {
                    attackDamage = Math.floor(attackDamage * 1.5); // 1.5x damage
                    isCriticalHit = true;

                    // Play critical damage sound if target is a player character
                    if (!actualTarget.type && window.soundManager) {
                        soundManager.playCriticalDamageSound();
                        criticalHitAgainstPlayer = true;
                    }
                }

                // Apply class-specific damage modifiers
                if (actor.type === 'Wing Lizard' &&
                    actor.specialAbilities &&
                    actor.specialAbilities.rangerWeakness &&
                    actualTarget.class === 'Ranger') {
                    attackDamage = Math.floor(attackDamage * actor.specialAbilities.rangerWeakness);
                }

                // Apply defending bonus
                let defenseReduction = actualTarget.defending ? attackDamage * 0.5 : 0;

                // Apply PROTECT effect (doubles physical defense)
                if (actualTarget.protected && actualTarget.protectedTurnsLeft > 0) {
                    defenseReduction *= 2; // Double the defense reduction
                }

                // Apply SHIELD damage reduction if target is shield bearer
                let finalDamage = Math.max(1, attackDamage - defenseReduction);
                if (actualTarget === shieldBearer && shieldBearer.shieldActive) {
                    finalDamage = Math.floor(finalDamage * CONSTANTS.SKILLS.LEVEL_5_SKILLS.SHIELD.damageReduction);
                    this.addToLog(`🛡️ SHIELD reduces damage to ${finalDamage}!`, 'system');
                }

                // Log protection effect if active
                if (actualTarget.protected && actualTarget.protectedTurnsLeft > 0 && defenseReduction > (actualTarget.defending ? attackDamage * 0.5 : 0)) {
                    this.addToLog(`🛡️ ${actualTarget.name}'s PROTECT effect reduces damage!`, 'system');
                }

                // Apply physical damage resistance for enemies (like Golem)
                if (actualTarget.type && actualTarget.specialAbilities && actualTarget.specialAbilities.physicalDamageReduction) {
                    const originalDamage = finalDamage;
                    finalDamage = Math.floor(finalDamage * (1 - actualTarget.specialAbilities.physicalDamageReduction));
                    finalDamage = Math.max(1, finalDamage); // Ensure at least 1 damage
                    this.addToLog(`🛡️ ${actualTarget.name}'s tough hide reduces damage from ${originalDamage} to ${finalDamage}!`, 'system');
                }

                actualTarget.life = Math.max(0, actualTarget.life - finalDamage);

                // Check for life drain ability (any monster with lifeDrain special ability)
                if (actor.specialAbilities &&
                    actor.specialAbilities.lifeDrain &&
                    finalDamage > 0) {
                    const drainAmount = Math.floor(finalDamage * actor.specialAbilities.lifeDrain);
                    const oldLife = actor.life;
                    actor.life = Math.min(actor.maxLife, actor.life + drainAmount);
                    const actualDrain = actor.life - oldLife;

                    if (actualDrain > 0) {
                        this.addToLog(`🩸 ${actor.name} drains ${actualDrain} life! 🩸`, 'system');
                    }
                }

                if (hasBoost) {
                    this.addToLog(`💥 ${actor.name} unleashes a concentrated attack for ${finalDamage} damage! 💥`, 'damage');
                    characterSystem.setDamageBoost(actor, false); // Clear boost after use
                } else if (isCriticalHit) {
                    this.addToLog(`⚡ ${actor.name} lands a CRITICAL HIT for ${finalDamage} damage! ⚡`, 'damage');
                } else {
                    this.addToLog(`${actor.name} attacks for ${finalDamage} damage!`, 'damage');
                }

                // Play attack hit sound effect based on character class
                // Skip normal sound if critical hit sound was already played against player
                if (window.soundManager && !criticalHitAgainstPlayer) {
                    if (actor.type) {
                        // Monster attack
                        soundManager.playCombatSound('monster_physical_attack');
                    } else {
                        // Player attack - determine character class
                        const characterClass = actor.name.toUpperCase();
                        soundManager.playCombatSound('player_physical_attack', characterClass);
                    }
                }

                // Check for stun ability (Giant, Troll, and Greater Demon monsters)
                if (actor.specialAbilities &&
                    actor.specialAbilities.stunChance &&
                    finalDamage > 0 &&
                    actualTarget.life > 0 &&
                    Math.random() < actor.specialAbilities.stunChance) {

                    // Apply paralysis (stun) to the target for 3-5 turns
                    const stunDuration = 3 + Math.floor(Math.random() * 3); // 3-5 turns
                    if (window.characterSystem) {
                        const paralysisResult = characterSystem.applyParalysis(actualTarget, stunDuration);

                        // Different messages based on monster type
                        if (actor.type === 'Giant') {
                            this.addToLog(`💥 ${actor.name}'s massive blow STUNS ${actualTarget.name || 'Enemy'}! 💥`, 'system');
                        } else if (actor.type === 'Troll') {
                            this.addToLog(`💥 ${actor.name}'s brutal strike STUNS ${actualTarget.name || 'Enemy'}! 💥`, 'system');
                        } else if (actor.type === 'Greater Demon') {
                            this.addToLog(`👹 ${actor.name}'s otherworldly strike PARALYZES ${actualTarget.name || 'Enemy'}! 👹`, 'system');
                        } else {
                            this.addToLog(`💥 ${actor.name}'s powerful attack STUNS ${actualTarget.name || 'Enemy'}! 💥`, 'system');
                        }

                        this.addToLog(paralysisResult.message, 'system');
                        this.addToLog(`😵 ${actualTarget.name || 'Enemy'} is stunned for ${stunDuration} turns! 😵`, 'system');
                    }
                }
                break;
                
            case 'magic':
                // Check for damage boost from concentration
                const hasMagicBoost = actor.damageBoost;
                const magicBoostMultiplier = hasMagicBoost ? CONSTANTS.SKILLS.CONCENTRATE_DAMAGE_MULTIPLIER : 1;
                
                const magicEvasionChance = hasMagicBoost ? 0 : this.calculateEvasionChance(target.agl, actor.agl) * CONSTANTS.COMBAT.MAGIC_EVASION_MODIFIER;
                if (Math.random() < magicEvasionChance) {
                    this.addToLog(`${target.name || 'Enemy'} evades ${actor.name}'s magic!`, 'system');
                    // Play evade sound effect for magic
                    if (window.soundManager) {
                        soundManager.playEvade();
                    }
                    break;
                }
                
                let magicDamage = Math.floor(actor.int * (0.7 + Math.random() * 0.6) * magicBoostMultiplier);

                // Check for critical hit on magic attacks
                let isMagicCriticalHit = false;
                let magicCriticalHitAgainstPlayer = false;
                if (actor.specialAbilities &&
                    actor.specialAbilities.criticalHitChance &&
                    Math.random() < actor.specialAbilities.criticalHitChance) {
                    magicDamage = Math.floor(magicDamage * 1.5); // 1.5x damage
                    isMagicCriticalHit = true;

                    // Play critical damage sound if target is a player character
                    if (!actualTarget.type && window.soundManager) {
                        soundManager.playCriticalDamageSound();
                        magicCriticalHitAgainstPlayer = true;
                    }
                }

                const finalMagicDamage = Math.max(1, magicDamage - (target.defending ? magicDamage * 0.3 : 0));
                target.life = Math.max(0, target.life - finalMagicDamage);

                if (hasMagicBoost) {
                    this.addToLog(`💫 ${actor.name} unleashes concentrated magic for ${finalMagicDamage} damage! 💫`, 'damage');
                    characterSystem.setDamageBoost(actor, false); // Clear boost after use
                } else if (isMagicCriticalHit) {
                    this.addToLog(`⚡ ${actor.name} casts a CRITICAL magic spell for ${finalMagicDamage} damage! ⚡`, 'damage');
                } else {
                    this.addToLog(`${actor.name} casts magic for ${finalMagicDamage} damage!`, 'damage');
                }

                // Check for life drain ability on magic attacks (any monster with lifeDrain special ability)
                if (actor.specialAbilities &&
                    actor.specialAbilities.lifeDrain &&
                    finalMagicDamage > 0) {
                    const drainAmount = Math.floor(finalMagicDamage * actor.specialAbilities.lifeDrain);
                    const oldLife = actor.life;
                    actor.life = Math.min(actor.maxLife, actor.life + drainAmount);
                    const actualDrain = actor.life - oldLife;

                    if (actualDrain > 0) {
                        this.addToLog(`🩸 ${actor.name} drains ${actualDrain} life! 🩸`, 'system');
                    }
                }

                // Play magic hit sound effect
                // Skip normal sound if critical hit sound was already played against player
                if (window.soundManager && !magicCriticalHitAgainstPlayer) {
                    soundManager.playCombatSound('magic_attack');
                }

                // Check for scream ability (Fiend 15% chance, Stalker 20% chance on any attack)
                if ((actor.type === 'Fiend' || actor.type === 'Stalker') &&
                    actor.specialAbilities &&
                    actor.specialAbilities.screamChance &&
                    Math.random() < actor.specialAbilities.screamChance) {

                    // Apply paralysis to the target
                    if (window.characterSystem && target.life > 0) {
                        const paralysisResult = characterSystem.applyParalysis(target, 1);
                        if (actor.type === 'Stalker') {
                            this.addToLog(`👻 ${actor.name} lets out a HAUNTING SCREAM! 👻`, 'system');
                        } else {
                            this.addToLog(`⚡ ${actor.name} lets out a terrifying SCREAM! ⚡`, 'system');
                        }
                        this.addToLog(paralysisResult.message, 'system');
                    }
                }
                break;
                
            case 'defend':
                actor.defending = true;
                this.addToLog(`${actor.name} takes a defensive stance.`, 'system');
                // Play defend sound effect
                if (window.soundManager) {
                    soundManager.playDefend();
                }
                break;
                
            case 'flee':
                const partyAgl = gameState.party.reduce((sum, member) => sum + member.agl, 0) / gameState.party.length;
                const fleeChance = Math.min(0.8, partyAgl / (partyAgl + target.agl));
                if (Math.random() < fleeChance) {
                    this.addToLog('Successfully fled from battle!', 'system');
                    // Play flee success sound effect
                    if (window.soundManager) {
                        soundManager.playFleeSuccess();
                    }
                    this.endCombat(false);
                    return;
                } else {
                    this.addToLog('Could not escape!', 'system');
                }
                break;
                
            case 'dark_wave':
                this.addToLog(`${actor.name} gathers dark energy...`, 'system');
                this.addToLog('🌑 DARK WAVE! 🌑 A wave of darkness engulfs the party!', 'damage');
                
                // Apply damage to all alive party members
                const alivePartyMembers = gameState.getAlivePartyMembers();
                let totalDamageDealt = 0;
                
                alivePartyMembers.forEach(member => {
                    // Calculate approximately 10% of max life as damage
                    const baseDamage = Math.floor(member.maxLife * 0.1);
                    // Add some variance (8-12% of max life)
                    const variance = Math.floor(member.maxLife * 0.02 * (Math.random() * 2 - 1));
                    const finalDamage = Math.max(1, baseDamage + variance);
                    
                    // Apply damage but don't kill (minimum 1 life)
                    const oldLife = member.life;
                    member.life = Math.max(1, member.life - finalDamage);
                    const actualDamage = oldLife - member.life;
                    
                    totalDamageDealt += actualDamage;
                    this.addToLog(`${member.name} takes ${actualDamage} dark damage!`, 'damage');
                });
                
                if (totalDamageDealt > 0) {
                    this.addToLog(`The dark wave dealt ${totalDamageDealt} total damage to the party!`, 'damage');
                }
                
                // Play special attack sound effect
                if (window.soundManager) {
                    soundManager.playCombatSound('magic_attack');
                }
                break;
                
            case 'concentrate':
                characterSystem.setConcentrated(actor, true);
                this.addToLog(`⚡ ${actor.name} focuses their mind and concentrates! ⚡`, 'system');
                
                // Play defend sound effect for concentration
                if (window.soundManager) {
                    soundManager.playDefend();
                }
                break;
                
            case 'skill':
                // Check if character can use skills
                if (!characterSystem.canUseSkills(actor)) {
                    this.addToLog(`${actor.name} has not learned any skills yet!`, 'system');
                    break;
                }
                
                // Check if character was concentrated
                if (actor.concentrated) {
                    // Use the selected skill (stored in currentSelectedSkill)
                    const skill = this.currentSelectedSkill;
                    if (skill) {
                        let skillResult;

                        if (skill.id === 'first_aid') {
                            // FIRST AID requires target selection - use provided target
                            skillResult = characterSystem.executeSkill(skill.id, actor, target);
                        } else if (skill.id === 'triple_strike') {
                            // TRIPLE STRIKE requires target selection and special handling
                            skillResult = characterSystem.executeSkill(skill.id, actor, target);
                            if (skillResult.success && skillResult.skillType === 'triple_strike') {
                                // Execute triple strike attacks
                                this.executeTripleStrike(actor, target);
                                characterSystem.setConcentrated(actor, false); // Clear concentration
                                this.currentSelectedSkill = null; // Clear selected skill
                                return; // Skip normal skill result message and rest of processing
                            }
                        } else if (skill.id === 'exorcise') {
                            // EXORCISE requires target selection
                            skillResult = characterSystem.executeSkill(skill.id, actor, target);
                        } else if (skill.id === 'melee') {
                            // MELEE requires special handling for area damage
                            skillResult = characterSystem.executeSkill(skill.id, actor);
                            if (skillResult.success && skillResult.skillType === 'melee') {
                                // Execute melee area attack
                                this.executeMeleeAttack(actor);
                                characterSystem.setConcentrated(actor, false); // Clear concentration
                                this.currentSelectedSkill = null; // Clear selected skill
                                return; // Skip normal skill result message and rest of processing
                            }
                        } else if (skill.id === 'critical_shot') {
                            // CRITICAL SHOT requires target selection and special handling
                            skillResult = characterSystem.executeSkill(skill.id, actor, target);
                            if (skillResult.success && skillResult.skillType === 'critical_shot') {
                                // Execute critical shot
                                this.executeCriticalShot(actor, target);
                                characterSystem.setConcentrated(actor, false); // Clear concentration
                                this.currentSelectedSkill = null; // Clear selected skill
                                return; // Skip normal skill result message and rest of processing
                            }
                        } else if (skill.id === 'dragon_strike') {
                            // DRAGON STRIKE requires target selection and special handling
                            skillResult = characterSystem.executeSkill(skill.id, actor, target);
                            if (skillResult.success && skillResult.skillType === 'dragon_strike') {
                                // Execute dragon strike
                                this.executeDragonStrike(actor, target, skillResult);
                                characterSystem.setConcentrated(actor, false); // Clear concentration
                                this.currentSelectedSkill = null; // Clear selected skill
                                return; // Skip normal skill result message and rest of processing
                            }
                        } else if (skill.id === 'healing_light') {
                            // HEALING LIGHT requires special handling for incapacitation
                            skillResult = characterSystem.executeSkill(skill.id, actor);
                            if (skillResult.success && skillResult.skillType === 'healing_light') {
                                // HEALING LIGHT is executed immediately in Character.js
                                // Just display the result and handle the incapacitation
                                this.addToLog(skillResult.message, 'system');
                                characterSystem.setConcentrated(actor, false); // Clear concentration
                                this.currentSelectedSkill = null; // Clear selected skill
                                this.updateCombatUI(); // Update UI to reflect healing and incapacitation
                                return; // Skip normal skill result message and rest of processing
                            }
                        } else if (skill.id === 'protect') {
                            // PROTECT requires special handling for party-wide effect and incapacitation
                            skillResult = characterSystem.executeSkill(skill.id, actor);
                            if (skillResult.success && skillResult.skillType === 'protect') {
                                // PROTECT is executed immediately in Character.js
                                // Just display the result and handle the incapacitation
                                this.addToLog(skillResult.message, 'system');
                                characterSystem.setConcentrated(actor, false); // Clear concentration
                                this.currentSelectedSkill = null; // Clear selected skill
                                this.updateCombatUI(); // Update UI to reflect protection and incapacitation
                                return; // Skip normal skill result message and rest of processing
                            }
                        } else if (skill.id === 'swift_movement') {
                            // SWIFT MOVEMENT requires special handling for agility boost
                            skillResult = characterSystem.executeSkill(skill.id, actor);
                            if (skillResult.success && skillResult.skillType === 'swift_movement') {
                                // SWIFT MOVEMENT is executed immediately in Character.js
                                // Just display the result and update turn order for agility change
                                this.addToLog(skillResult.message, 'system');
                                characterSystem.setConcentrated(actor, false); // Clear concentration
                                this.currentSelectedSkill = null; // Clear selected skill
                                // Refresh turn order to account for agility change
                                this.setupTurnOrder();
                                this.updateCombatUI(); // Update UI to reflect agility boost
                                return; // Skip normal skill result message and rest of processing
                            }
                        } else {
                            // SHIELD, SILENCE, and HIDE don't need targets
                            skillResult = characterSystem.executeSkill(skill.id, actor);
                        }
                        
                        if (skillResult.success) {
                            this.addToLog(skillResult.message, 'system');
                        } else {
                            this.addToLog(skillResult.message, 'system');
                        }

                        // Clear concentration and selected skill after use
                        characterSystem.setConcentrated(actor, false);
                        this.currentSelectedSkill = null;
                    } else {
                        // No skill was selected through the menu - fallback behavior
                        this.addToLog(`${actor.name} tried to use a skill but none was selected!`, 'error');
                        characterSystem.setConcentrated(actor, false);
                    }
                } else {
                    // Give damage boost instead of using skill
                    characterSystem.setDamageBoost(actor, true);
                    this.addToLog(`🔥 ${actor.name} channels their power for a devastating next attack! 🔥`, 'system');
                }
                
                // Play magic hit sound effect for skills
                if (window.soundManager) {
                    soundManager.playCombatSound('magic_attack');
                }
                break;

            case 'poison_attack':
                // Poison attack - deals damage and applies poison
                const poisonEvasionChance = this.calculateEvasionChance(target.agl, actor.agl);
                if (Math.random() < poisonEvasionChance) {
                    this.addToLog(`${target.name || 'Enemy'} evades ${actor.name}'s poison attack!`, 'system');
                    if (window.soundManager) {
                        soundManager.playEvade();
                    }
                    break;
                }

                // Calculate poison attack damage (weaker than normal attack)
                const poisonDamage = Math.floor(actor.str * 0.7); // 70% of normal attack damage
                const finalPoisonDamage = Math.max(1, poisonDamage);

                target.life = Math.max(0, target.life - finalPoisonDamage);

                // Check for life drain ability on poison attacks (any monster with lifeDrain special ability)
                if (actor.specialAbilities &&
                    actor.specialAbilities.lifeDrain &&
                    finalPoisonDamage > 0) {
                    const drainAmount = Math.floor(finalPoisonDamage * actor.specialAbilities.lifeDrain);
                    const oldLife = actor.life;
                    actor.life = Math.min(actor.maxLife, actor.life + drainAmount);
                    const actualDrain = actor.life - oldLife;

                    if (actualDrain > 0) {
                        this.addToLog(`🩸 ${actor.name} drains ${actualDrain} life! 🩸`, 'system');
                    }
                }

                // Apply poison effect
                if (window.characterSystem && target.life > 0) {
                    characterSystem.applyPoison(target, 5); // 5 turns of poison
                    this.addToLog(`💚 ${actor.name} attacks with poison for ${finalPoisonDamage} damage and poisons ${target.name}! 💚`, 'damage');
                } else {
                    this.addToLog(`${actor.name} attacks with poison for ${finalPoisonDamage} damage!`, 'damage');
                }

                // Play poison attack sound effect
                if (window.soundManager) {
                    soundManager.playCombatSound('poison_attack');
                }
                break;

            case 'breath_attack':
                // Wing Lizard's breath attack - damages all party members
                this.addToLog(`🐲 ${actor.name} unleashes a devastating breath attack! 🐲`, 'system');

                const breathTargets = gameState.getAlivePartyMembers();
                let totalBreathDamage = 0;

                breathTargets.forEach(member => {
                    // Calculate breath damage (80% of normal attack damage)
                    const baseDamage = Math.floor(actor.str * 0.8);
                    // Add some variance (70-90% of base damage)
                    const variance = Math.floor(baseDamage * 0.2 * (Math.random() * 2 - 1));
                    const breathDamage = Math.max(1, baseDamage + variance);

                    // Apply defending bonus
                    let defenseReduction = member.defending ? breathDamage * 0.5 : 0;

                    // Apply PROTECT effect (doubles physical defense)
                    if (member.protected && member.protectedTurnsLeft > 0) {
                        defenseReduction *= 2; // Double the defense reduction
                    }

                    const finalBreathDamage = Math.max(1, breathDamage - defenseReduction);
                    member.life = Math.max(0, member.life - finalBreathDamage);
                    totalBreathDamage += finalBreathDamage;

                    // Log protection effect if active
                    if (member.protected && member.protectedTurnsLeft > 0 && defenseReduction > (member.defending ? breathDamage * 0.5 : 0)) {
                        this.addToLog(`🛡️ ${member.name}'s PROTECT effect reduces damage!`, 'system');
                    }

                    this.addToLog(`${member.name} takes ${finalBreathDamage} breath damage!`, 'damage');
                });

                if (totalBreathDamage > 0) {
                    this.addToLog(`The breath attack dealt ${totalBreathDamage} total damage to the party!`, 'damage');
                }

                // Play breath attack sound effect
                if (window.soundManager) {
                    soundManager.playCombatSound('breath_attack');
                }
                break;

            case 'scream_attack':
                // Fiend's and Phantom's scream attack - magic damage with paralysis effect
                const screamEvasionChance = this.calculateEvasionChance(target.agl, actor.agl) * CONSTANTS.COMBAT.MAGIC_EVASION_MODIFIER;
                if (Math.random() < screamEvasionChance) {
                    this.addToLog(`${target.name || 'Enemy'} evades ${actor.name}'s terrifying scream!`, 'system');
                    if (window.soundManager) {
                        soundManager.playEvade();
                    }
                    break;
                }

                // Calculate scream damage (magic-based)
                const screamDamage = Math.floor(actor.int * (0.6 + Math.random() * 0.5));
                const finalScreamDamage = Math.max(1, screamDamage);

                target.life = Math.max(0, target.life - finalScreamDamage);

                // Different messages for different enemies
                if (actor.type === 'Phantom') {
                    this.addToLog(`👻 ${actor.name} lets out a HAUNTING SCREAM for ${finalScreamDamage} damage! 👻`, 'damage');
                } else if (actor.type === 'Stalker') {
                    this.addToLog(`👻 ${actor.name} emits a CHILLING SCREAM for ${finalScreamDamage} damage! 👻`, 'damage');
                } else if (actor.type === 'Vampire Lord') {
                    this.addToLog(`🧛 ${actor.name} unleashes a BLOOD-CURDLING SCREAM for ${finalScreamDamage} damage! 🧛`, 'damage');
                } else {
                    this.addToLog(`⚡ ${actor.name} unleashes a TERRIFYING SCREAM for ${finalScreamDamage} damage! ⚡`, 'damage');
                }

                // Apply paralysis effect
                if (target.life > 0 && window.characterSystem) {
                    const paralysisResult = characterSystem.applyParalysis(target, 1);
                    this.addToLog(paralysisResult.message, 'system');
                }

                // Check for life drain ability on scream attacks (any monster with lifeDrain special ability)
                if (actor.specialAbilities &&
                    actor.specialAbilities.lifeDrain &&
                    finalScreamDamage > 0) {
                    const drainAmount = Math.floor(finalScreamDamage * actor.specialAbilities.lifeDrain);
                    const oldLife = actor.life;
                    actor.life = Math.min(actor.maxLife, actor.life + drainAmount);
                    const actualDrain = actor.life - oldLife;

                    if (actualDrain > 0) {
                        this.addToLog(`🩸 ${actor.name} drains ${actualDrain} life! 🩸`, 'system');
                    }
                }

                // Play magic hit sound effect
                if (window.soundManager) {
                    soundManager.playCombatSound('magic_attack');
                }
                break;

            case 'spider_web_attack':
                // Giant Spider's spider web attack - no damage but causes paralysis for 3-6 turns
                const webEvasionChance = this.calculateEvasionChance(target.agl, actor.agl);
                if (Math.random() < webEvasionChance) {
                    this.addToLog(`${target.name || 'Enemy'} dodges ${actor.name}'s spider web!`, 'system');
                    if (window.soundManager) {
                        soundManager.playEvade();
                    }
                    break;
                }

                // Spider web attack causes no damage but applies paralysis for 3-6 turns
                const webDuration = 3 + Math.floor(Math.random() * 4); // 3-6 turns
                this.addToLog(`🕷️ ${actor.name} shoots a sticky web at ${target.name || 'Enemy'}! 🕷️`, 'system');

                // Apply paralysis effect
                if (target.life > 0 && window.characterSystem) {
                    const paralysisResult = characterSystem.applyParalysis(target, webDuration);
                    this.addToLog(paralysisResult.message, 'system');
                    this.addToLog(`🕸️ ${target.name || 'Enemy'} is trapped in the web for ${webDuration} turns! 🕸️`, 'system');
                }

                // Play magic hit sound effect (web shooting sound)
                if (window.soundManager) {
                    soundManager.playCombatSound('magic_attack');
                }
                break;

            case 'area_magic_attack':
                // Demon's area magic attack - damages all party members with magic
                this.addToLog(`👹 ${actor.name} gathers dark magical energy... 👹`, 'system');
                this.addToLog('🌟 INFERNAL BLAST! 🌟 Dark magic engulfs the entire party!', 'damage');

                const areaMagicTargets = gameState.getAlivePartyMembers();
                let totalAreaMagicDamage = 0;

                areaMagicTargets.forEach(member => {
                    // Calculate area magic damage (based on INT, similar to normal magic but slightly reduced)
                    const baseDamage = Math.floor(actor.int * (0.6 + Math.random() * 0.4));
                    // Add some variance (50-90% of base damage)
                    const variance = Math.floor(baseDamage * 0.4 * (Math.random() * 2 - 1));
                    const areaMagicDamage = Math.max(1, baseDamage + variance);

                    // Apply defending bonus (magic attacks get 30% reduction from defend)
                    let defenseReduction = member.defending ? areaMagicDamage * 0.3 : 0;

                    // Apply SHIELD damage reduction if member is shield bearer
                    let finalAreaMagicDamage = Math.max(1, areaMagicDamage - defenseReduction);
                    if (member.shieldActive && member.shieldTurnsLeft > 0) {
                        finalAreaMagicDamage = Math.floor(finalAreaMagicDamage * CONSTANTS.SKILLS.LEVEL_5_SKILLS.SHIELD.damageReduction);
                        this.addToLog(`🛡️ ${member.name}'s SHIELD reduces magic damage to ${finalAreaMagicDamage}!`, 'system');
                    }

                    member.life = Math.max(0, member.life - finalAreaMagicDamage);
                    totalAreaMagicDamage += finalAreaMagicDamage;

                    this.addToLog(`${member.name} takes ${finalAreaMagicDamage} dark magic damage!`, 'damage');
                });

                if (totalAreaMagicDamage > 0) {
                    this.addToLog(`The infernal blast dealt ${totalAreaMagicDamage} total damage to the party!`, 'damage');
                }

                // Check for life drain ability on area magic attacks
                if (actor.specialAbilities &&
                    actor.specialAbilities.lifeDrain &&
                    totalAreaMagicDamage > 0) {
                    const drainAmount = Math.floor(totalAreaMagicDamage * actor.specialAbilities.lifeDrain);
                    const oldLife = actor.life;
                    actor.life = Math.min(actor.maxLife, actor.life + drainAmount);
                    const actualDrain = actor.life - oldLife;

                    if (actualDrain > 0) {
                        this.addToLog(`🩸 ${actor.name} drains ${actualDrain} life from the dark magic! 🩸`, 'system');
                    }
                }

                // Play magic hit sound effect
                if (window.soundManager) {
                    soundManager.playCombatSound('magic_attack');
                }
                break;
        }

        this.updateCombatUI();
    }

    // Execute Triple Strike skill (3 consecutive attacks)
    executeTripleStrike(warrior, target) {
        if (!target || target.life <= 0) {
            this.addToLog(`${warrior.name}'s TRIPLE STRIKE fails - no valid target!`, 'system');
            return;
        }

        this.addToLog(`⚔️ ${warrior.name} unleashes TRIPLE STRIKE against ${target.name}! ⚔️`, 'system');

        // Execute 3 consecutive attacks
        for (let i = 1; i <= 3; i++) {
            if (target.life <= 0) {
                this.addToLog(`${target.name} is defeated before all strikes can land!`, 'system');
                break;
            }

            // Calculate attack damage (same as normal attack)
            const baseDamage = warrior.str;
            const variance = Math.floor(baseDamage * 0.2 * (Math.random() * 2 - 1));
            let attackDamage = Math.max(1, baseDamage + variance);

            // Apply defending bonus
            let defenseReduction = target.defending ? attackDamage * 0.5 : 0;

            // Apply PROTECT effect (doubles physical defense)
            if (target.protected && target.protectedTurnsLeft > 0) {
                defenseReduction *= 2; // Double the defense reduction
            }

            // Apply SHIELD damage reduction if target is shield bearer
            if (target.shieldActive && target.shieldTurnsLeft > 0) {
                defenseReduction += attackDamage * (1 - CONSTANTS.SKILLS.LEVEL_5_SKILLS.SHIELD.damageReduction);
            }

            const finalDamage = Math.max(1, attackDamage - defenseReduction);
            target.life = Math.max(0, target.life - finalDamage);

            // Log protection effect if active
            if (target.protected && target.protectedTurnsLeft > 0 && defenseReduction > (target.defending ? attackDamage * 0.5 : 0)) {
                this.addToLog(`🛡️ ${target.name}'s PROTECT effect reduces damage!`, 'system');
            }

            this.addToLog(`Strike ${i}: ${warrior.name} deals ${finalDamage} damage to ${target.name}!`, 'damage');

            // Small delay between strikes for visual effect
            if (i < 3 && target.life > 0) {
                // In a real implementation, you might want to add visual delays
                // For now, we'll just continue immediately
            }
        }

        // Play attack hit sound effect for triple strike
        if (window.soundManager) {
            soundManager.playCombatSound('player_physical_attack', 'WARRIOR');
        }

        this.updateCombatUI();
    }

    // Execute Melee skill (area attack to all enemies)
    executeMeleeAttack(warrior) {
        const aliveEnemies = this.enemies.filter(enemy => enemy.life > 0);

        if (aliveEnemies.length === 0) {
            this.addToLog(`${warrior.name}'s MELEE attack finds no targets!`, 'system');
            return;
        }

        this.addToLog(`⚔️ ${warrior.name} unleashes MELEE! All enemies take massive damage! ⚔️`, 'system');

        // Attack each alive enemy with 2x damage
        aliveEnemies.forEach(enemy => {
            // Calculate base damage (same as normal attack but doubled)
            const baseDamage = warrior.str * CONSTANTS.SKILLS.LEVEL_15_SKILLS.MELEE.damageMultiplier;
            const variance = Math.floor(baseDamage * 0.2 * (Math.random() * 2 - 1));
            let attackDamage = Math.max(1, baseDamage + variance);

            // Apply defending bonus if enemy is defending
            let defenseReduction = enemy.defending ? attackDamage * 0.5 : 0;

            // Apply PROTECT effect (doubles physical defense)
            if (enemy.protected && enemy.protectedTurnsLeft > 0) {
                defenseReduction *= 2; // Double the defense reduction
            }

            const finalDamage = Math.max(1, attackDamage - defenseReduction);

            // Log protection effect if active
            if (enemy.protected && enemy.protectedTurnsLeft > 0 && defenseReduction > (enemy.defending ? attackDamage * 0.5 : 0)) {
                this.addToLog(`🛡️ ${enemy.name}'s PROTECT effect reduces damage!`, 'system');
            }

            enemy.life = Math.max(0, enemy.life - finalDamage);
            this.addToLog(`${enemy.name} takes ${finalDamage} damage from MELEE!`, 'damage');
        });

        // Play attack hit sound effect for melee
        if (window.soundManager) {
            soundManager.playCombatSound('player_physical_attack', 'WARRIOR');
        }

        this.updateCombatUI();
    }

    // Execute Critical Shot skill (30% instant kill or normal damage)
    executeCriticalShot(ranger, target) {
        if (!target || target.life <= 0) {
            this.addToLog(`${ranger.name}'s CRITICAL SHOT fails - no valid target!`, 'system');
            return;
        }

        // Double-check dragon immunity (safety check)
        if (target.properties && target.properties.includes('dragon')) {
            this.addToLog(`💨 ${target.name} is immune to CRITICAL SHOT! Dragons cannot be instantly defeated! 💨`, 'system');
            // Fall back to normal damage attack
            const baseDamage = ranger.str;
            const variance = Math.floor(baseDamage * 0.2 * (Math.random() * 2 - 1));
            let attackDamage = Math.max(1, baseDamage + variance);

            // Apply defending bonus if target is defending
            let defenseReduction = target.defending ? attackDamage * 0.5 : 0;

            // Apply PROTECT effect (doubles physical defense)
            if (target.protected && target.protectedTurnsLeft > 0) {
                defenseReduction *= 2; // Double the defense reduction
            }

            const finalDamage = Math.max(1, attackDamage - defenseReduction);

            // Log protection effect if active
            if (target.protected && target.protectedTurnsLeft > 0 && defenseReduction > (target.defending ? attackDamage * 0.5 : 0)) {
                this.addToLog(`🛡️ ${target.name}'s PROTECT effect reduces damage!`, 'system');
            }

            target.life = Math.max(0, target.life - finalDamage);
            this.addToLog(`🎯 ${ranger.name} attacks ${target.name} for ${finalDamage} damage instead!`, 'damage');
        } else {
            // Check for instant kill chance (30%)
            const instantKillRoll = Math.random();
            if (instantKillRoll < CONSTANTS.SKILLS.LEVEL_15_SKILLS.CRITICAL_SHOT.instantKillChance) {
                // Instant kill successful
                target.life = 0;
                this.addToLog(`💀 CRITICAL SHOT! ${target.name} is instantly defeated! 💀`, 'damage');
            } else {
                // Normal damage attack
                const baseDamage = ranger.str;
                const variance = Math.floor(baseDamage * 0.2 * (Math.random() * 2 - 1));
                let attackDamage = Math.max(1, baseDamage + variance);

                // Apply defending bonus if target is defending
                let defenseReduction = target.defending ? attackDamage * 0.5 : 0;

                // Apply PROTECT effect (doubles physical defense)
                if (target.protected && target.protectedTurnsLeft > 0) {
                    defenseReduction *= 2; // Double the defense reduction
                }

                const finalDamage = Math.max(1, attackDamage - defenseReduction);

                // Log protection effect if active
                if (target.protected && target.protectedTurnsLeft > 0 && defenseReduction > (target.defending ? attackDamage * 0.5 : 0)) {
                    this.addToLog(`🛡️ ${target.name}'s PROTECT effect reduces damage!`, 'system');
                }

                target.life = Math.max(0, target.life - finalDamage);
                this.addToLog(`🎯 CRITICAL SHOT deals ${finalDamage} damage to ${target.name}!`, 'damage');
            }
        }

        // Play attack hit sound effect for critical shot
        if (window.soundManager) {
            soundManager.playCombatSound('player_physical_attack', 'RANGER');
        }

        this.updateCombatUI();
    }

    // Execute Dragon Strike skill (enhanced damage vs dragons)
    executeDragonStrike(warrior, target, skillResult) {
        if (!target || target.life <= 0) {
            this.addToLog(`${warrior.name}'s DRAGON STRIKE fails - no valid target!`, 'system');
            return;
        }

        this.addToLog(skillResult.message, 'system');

        // Calculate base damage (same as normal attack)
        const baseDamage = warrior.str;
        const variance = Math.floor(baseDamage * 0.2 * (Math.random() * 2 - 1));
        let attackDamage = Math.max(1, baseDamage + variance);

        // Apply dragon damage multiplier if target is a dragon
        if (skillResult.isDragon) {
            attackDamage = Math.floor(attackDamage * skillResult.damageMultiplier);
            this.addToLog(`🐉 DRAGON STRIKE deals massive damage to the dragon! 🐉`, 'system');
        }

        // Apply defending bonus if target is defending
        let defenseReduction = target.defending ? attackDamage * 0.5 : 0;

        // Apply PROTECT effect (doubles physical defense)
        if (target.protected && target.protectedTurnsLeft > 0) {
            defenseReduction *= 2; // Double the defense reduction
        }

        // Apply SHIELD damage reduction if target is shield bearer
        if (target.shieldActive && target.shieldTurnsLeft > 0) {
            defenseReduction += attackDamage * (1 - CONSTANTS.SKILLS.LEVEL_5_SKILLS.SHIELD.damageReduction);
        }

        const finalDamage = Math.max(1, attackDamage - defenseReduction);

        // Log protection effect if active
        if (target.protected && target.protectedTurnsLeft > 0 && defenseReduction > (target.defending ? attackDamage * 0.5 : 0)) {
            this.addToLog(`🛡️ ${target.name}'s PROTECT effect reduces damage!`, 'system');
        }

        target.life = Math.max(0, target.life - finalDamage);

        if (skillResult.isDragon) {
            this.addToLog(`🐉 DRAGON STRIKE deals ${finalDamage} damage to ${target.name}! 🐉`, 'damage');
        } else {
            this.addToLog(`⚔️ DRAGON STRIKE deals ${finalDamage} damage to ${target.name}! ⚔️`, 'damage');
        }

        // Play attack hit sound effect for dragon strike
        if (window.soundManager) {
            soundManager.playCombatSound('player_physical_attack', 'WARRIOR');
        }

        this.updateCombatUI();
    }

    // Enemy AI turn
    enemyTurn() {
        const currentActor = this.turnOrder[this.currentTurnIndex];
        const currentEnemy = currentActor.member;
        
        let action = 'attack';
        let target = null;
        
        // Check if this is the Dark Lord and if it should use special attack
        if (currentEnemy.isFinalBoss && currentEnemy.type === 'Dark Lord') {
            currentEnemy.turnsSinceSpecialAttack++;
            
            if (currentEnemy.turnsSinceSpecialAttack >= currentEnemy.specialAttackInterval) {
                action = 'dark_wave';
                target = 'all_party'; // Special indicator for party-wide attack
                currentEnemy.turnsSinceSpecialAttack = 0;
            } else {
                // Normal AI behavior for Dark Lord
                const actions = ['attack', 'magic', 'defend'];
                const weights = [0.5, 0.4, 0.1];
                
                const rand = Math.random();
                let cumulative = 0;
                
                for (let i = 0; i < actions.length; i++) {
                    cumulative += weights[i];
                    if (rand < cumulative) {
                        action = actions[i];
                        break;
                    }
                }
            }
        } else {
            // Use monster-specific behavior pattern if available
            if (currentEnemy.behaviorPattern) {
                const pattern = currentEnemy.behaviorPattern;
                let actions = ['attack', 'magic', 'defend'];
                let weights = [pattern.attackWeight, pattern.magicWeight, pattern.defendWeight];

                // Check if enemy is silenced - cannot use magic
                if (characterSystem.isEnemySilenced(currentEnemy)) {
                    actions = ['attack', 'defend'];
                    weights = [pattern.attackWeight + pattern.magicWeight, pattern.defendWeight];
                }

                // Add poison attack if available
                if (pattern.poisonAttackWeight) {
                    actions.push('poison_attack');
                    weights.push(pattern.poisonAttackWeight);
                }

                // Add breath attack if available (Wing Lizard)
                if (pattern.breathAttackWeight) {
                    actions.push('breath_attack');
                    weights.push(pattern.breathAttackWeight);
                }

                // Add scream attack if available (Fiend)
                if (pattern.screamAttackWeight) {
                    actions.push('scream_attack');
                    weights.push(pattern.screamAttackWeight);
                }

                // Add spider web attack if available (Giant Spider)
                if (pattern.spiderWebAttackWeight) {
                    actions.push('spider_web_attack');
                    weights.push(pattern.spiderWebAttackWeight);
                }

                const rand = Math.random();
                let cumulative = 0;

                for (let i = 0; i < actions.length; i++) {
                    cumulative += weights[i];
                    if (rand < cumulative) {
                        action = actions[i];
                        break;
                    }
                }
            } else {
                // Default AI behavior for enemies without specific patterns
                let actions = ['attack', 'magic', 'defend'];
                let weights = [0.45, 0.35, 0.2];

                // Check if enemy is silenced - cannot use magic
                if (characterSystem.isEnemySilenced(currentEnemy)) {
                    actions = ['attack', 'defend'];
                    weights = [0.8, 0.2]; // Increase attack chance when silenced
                }

                const rand = Math.random();
                let cumulative = 0;

                for (let i = 0; i < actions.length; i++) {
                    cumulative += weights[i];
                    if (rand < cumulative) {
                        action = actions[i];
                        break;
                    }
                }
            }
        }
        
        // Select target if not already set
        if (!target && action !== 'breath_attack') {
            // Use targetable members (excludes hidden characters)
            const targetableMembers = characterSystem.getTargetablePartyMembers();
            if (targetableMembers.length === 0) {
                // If no targetable members, fall back to all alive members
                const aliveMembers = gameState.getAlivePartyMembers();
                if (aliveMembers.length === 0) return;
                target = aliveMembers[Math.floor(Math.random() * aliveMembers.length)];
            } else {
                target = targetableMembers[Math.floor(Math.random() * targetableMembers.length)];
            }
        }
        
        // Set waiting state and store action for later execution
        this.waitingForPlayerInput = true;
        this.pendingEnemyAction = { enemy: currentEnemy, target: target, action: action };
        this.updateCombatUI(); // Update UI to show "Press Enter or Click to continue..."
        
        // Auto-execute after 1 second if player doesn't skip
        this.enemyActionTimeout = setTimeout(() => {
            this.executeEnemyAction();
        }, 1000);
    }
    
    // Execute the pending enemy action
    executeEnemyAction() {
        if (!this.waitingForPlayerInput || !this.pendingEnemyAction) {
            return;
        }

        this.waitingForPlayerInput = false;
        if (this.enemyActionTimeout) {
            clearTimeout(this.enemyActionTimeout);
            this.enemyActionTimeout = null;
        }
        
        const { enemy, target, action } = this.pendingEnemyAction;
        this.pendingEnemyAction = null;
        
        this.executeAction(enemy, target, action);
        
        // Check for double action ability (Hell Hound, Killer Wolf, and Skeleton Knight)
        if ((enemy.type === 'Hell Hound' || enemy.type === 'Killer Wolf' || enemy.type === 'Skeleton Knight') &&
            enemy.behaviorPattern &&
            enemy.behaviorPattern.doubleActionChance &&
            !enemy.hasUsedDoubleAction) {

            if (Math.random() < enemy.behaviorPattern.doubleActionChance) {
                if (enemy.type === 'Hell Hound') {
                    this.addToLog(`🔥 ${enemy.name} howls and attacks again! 🔥`, 'system');
                } else if (enemy.type === 'Killer Wolf') {
                    this.addToLog(`🐺 ${enemy.name} snarls and strikes again! 🐺`, 'system');
                } else if (enemy.type === 'Skeleton Knight') {
                    this.addToLog(`💀 ${enemy.name} rattles and strikes again! 💀`, 'system');
                }
                enemy.hasUsedDoubleAction = true; // Prevent infinite double actions

                // Execute second action immediately
                setTimeout(() => {
                    this.performDoubleAction(enemy);
                }, 800);
                return;
            }
        }
        
        // Reset double action flag for next turn
        if (enemy.hasUsedDoubleAction) {
            enemy.hasUsedDoubleAction = false;
        }

        // Check for IMP flee ability
        if (enemy.type === 'Imp' &&
            enemy.specialAbilities &&
            enemy.specialAbilities.fleeChance &&
            Math.random() < enemy.specialAbilities.fleeChance) {

            this.addToLog(`👹 ${enemy.name} flees from battle! 👹`, 'system');

            // Remove the IMP from combat
            enemy.life = 0;

            // Play flee sound effect
            if (window.soundManager) {
                soundManager.playFleeSuccess();
            }

            this.nextTurn();
            return;
        }

        // Check for IMP ally summoning ability
        if (enemy.type === 'Imp' &&
            enemy.specialAbilities &&
            enemy.specialAbilities.allySummonChance &&
            !enemy.hasSummonedAlly &&
            Math.random() < enemy.specialAbilities.allySummonChance) {

            this.summonImpAlly(enemy);
            return; // Skip to next turn after summoning
        }

        // Check for Will-o'-the-wisp ally summoning ability
        if (enemy.type === 'Will-o\'-the-wisp' &&
            enemy.specialAbilities &&
            enemy.specialAbilities.allySummonChance &&
            !enemy.hasSummonedAlly) {

            // Check if this Will-o'-the-wisp is alone (only Will-o'-the-wisps in battle)
            const aliveEnemies = this.enemies.filter(e => e.life > 0);
            const willOWispCount = aliveEnemies.filter(e => e.type === 'Will-o\'-the-wisp').length;

            if (willOWispCount === 1 && Math.random() < enemy.specialAbilities.allySummonChance) {
                this.summonWillOWispAlly(enemy);
                return; // Skip to next turn after summoning
            }
        }

        // Check for area magic attack ability (DEMON, DARK_ANGEL, GREATER_DEMON, etc.)
        if (enemy.specialAbilities &&
            enemy.specialAbilities.areaMagicAttackChance &&
            Math.random() < enemy.specialAbilities.areaMagicAttackChance) {

            // Different messages based on monster type
            if (enemy.type === 'Demon') {
                this.addToLog(`👹 ${enemy.name} channels otherworldly power! 👹`, 'system');
            } else if (enemy.type === 'Demon Elite') {
                this.addToLog(`👹 ${enemy.name} unleashes elite demonic magic! 👹`, 'system');
            } else if (enemy.type === 'Dark Angel') {
                this.addToLog(`😇 ${enemy.name} spreads dark wings and unleashes divine wrath! 😇`, 'system');
            } else if (enemy.type === 'Greater Demon') {
                this.addToLog(`👹 ${enemy.name} summons the power of the abyss! 👹`, 'system');
            } else {
                this.addToLog(`✨ ${enemy.name} unleashes a powerful area magic attack! ✨`, 'system');
            }

            // Execute area magic attack immediately
            this.executeAction(enemy, 'all_party', 'area_magic_attack');
            this.nextTurn();
            return;
        }

        this.nextTurn();
    }
    
    // Perform double action for enemies with doubleActionChance (Hell Hound, Killer Wolf)
    performDoubleAction(enemy) {
        if (enemy.life <= 0) return; // Enemy died from counter-attack or similar
        
        // Use same behavior pattern for second action
        const pattern = enemy.behaviorPattern;
        const actions = ['attack', 'magic', 'defend'];
        const weights = [pattern.attackWeight, pattern.magicWeight, pattern.defendWeight];

        // Add poison attack if available
        if (pattern.poisonAttackWeight) {
            actions.push('poison_attack');
            weights.push(pattern.poisonAttackWeight);
        }

        // Add breath attack if available
        if (pattern.breathAttackWeight) {
            actions.push('breath_attack');
            weights.push(pattern.breathAttackWeight);
        }

        // Add scream attack if available
        if (pattern.screamAttackWeight) {
            actions.push('scream_attack');
            weights.push(pattern.screamAttackWeight);
        }

        // Add spider web attack if available
        if (pattern.spiderWebAttackWeight) {
            actions.push('spider_web_attack');
            weights.push(pattern.spiderWebAttackWeight);
        }

        let action = 'attack';
        const rand = Math.random();
        let cumulative = 0;

        for (let i = 0; i < actions.length; i++) {
            cumulative += weights[i];
            if (rand < cumulative) {
                action = actions[i];
                break;
            }
        }
        
        // Select random targetable party member as target
        const targetableMembers = characterSystem.getTargetablePartyMembers();
        let target;

        if (targetableMembers.length === 0) {
            // If no targetable members, fall back to all alive members
            const aliveMembers = gameState.getAlivePartyMembers();
            if (aliveMembers.length === 0) {
                this.nextTurn();
                return;
            }
            target = aliveMembers[Math.floor(Math.random() * aliveMembers.length)];
        } else {
            target = targetableMembers[Math.floor(Math.random() * targetableMembers.length)];
        }
        
        // Execute the second action
        this.executeAction(enemy, target, action);
        this.nextTurn();
    }
    
    // Skip enemy thinking phase (called by player input)
    skipEnemyThinking() {
        if (this.waitingForPlayerInput) {
            this.executeEnemyAction();
        }
    }

    // Summon an IMP ally
    summonImpAlly(summoner) {
        // Create a new IMP ally
        const impTemplate = CONSTANTS.MONSTER_TYPES.IMP;

        // Calculate stats similar to normal enemy generation
        const floorMultiplier = 1 + (gameState.currentFloor - 1) * 0.3;
        const baseStats = {
            life: Math.floor(impTemplate.baseStats.life * floorMultiplier),
            str: Math.floor(impTemplate.baseStats.str * floorMultiplier),
            int: Math.floor(impTemplate.baseStats.int * floorMultiplier),
            agl: Math.floor(impTemplate.baseStats.agl * floorMultiplier)
        };

        const variance = 0.25;
        const ally = {
            name: impTemplate.name,
            type: impTemplate.name,
            imagePath: impTemplate.imagePath,
            properties: impTemplate.properties || [],
            behaviorPattern: impTemplate.behaviorPattern,
            specialAbilities: impTemplate.specialAbilities,
            maxLife: Math.floor(baseStats.life * (1 + (Math.random() - 0.5) * variance)),
            str: Math.floor(baseStats.str * (1 + (Math.random() - 0.5) * variance)),
            int: Math.floor(baseStats.int * (1 + (Math.random() - 0.5) * variance)),
            agl: Math.floor(baseStats.agl * (1 + (Math.random() - 0.5) * variance)),
            hasSummonedAlly: true // Prevent summoned ally from summoning again
        };

        // Add random AGL bonus of 0-3 points
        ally.agl += Math.floor(Math.random() * 4);

        ally.life = ally.maxLife;
        ally.expReward = Math.floor((ally.maxLife + ally.str + ally.int + ally.agl) * gameState.currentFloor * 5.0);

        // Add the ally to the enemies array
        this.enemies.push(ally);

        // Mark the summoner as having summoned an ally
        summoner.hasSummonedAlly = true;

        // Log the summoning
        this.addToLog(`👹 ${summoner.name} summons another Imp to join the battle! 👹`, 'system');
        this.addToLog(`A ${ally.name} appears with a wicked grin!`, 'system');

        // Refresh turn order to include the new ally
        this.setupTurnOrder();

        // Update combat UI to show the new enemy
        this.updateCombatUI();

        // Play summon sound effect
        if (window.soundManager) {
            soundManager.playMagicHit();
        }

        this.nextTurn();
    }

    // Summon a Will-o'-the-wisp ally
    summonWillOWispAlly(summoner) {
        // Create a new Will-o'-the-wisp ally
        const willOWispTemplate = CONSTANTS.MONSTER_TYPES.WILL_O_THE_WISP;

        // Calculate stats similar to normal enemy generation
        const floorMultiplier = 1 + (gameState.currentFloor - 1) * 0.3;
        const baseStats = {
            life: Math.floor(willOWispTemplate.baseStats.life * floorMultiplier),
            str: Math.floor(willOWispTemplate.baseStats.str * floorMultiplier),
            int: Math.floor(willOWispTemplate.baseStats.int * floorMultiplier),
            agl: Math.floor(willOWispTemplate.baseStats.agl * floorMultiplier)
        };

        const variance = 0.25;
        const ally = {
            name: willOWispTemplate.name,
            type: willOWispTemplate.name,
            imagePath: willOWispTemplate.imagePath,
            properties: willOWispTemplate.properties || [],
            behaviorPattern: willOWispTemplate.behaviorPattern,
            specialAbilities: willOWispTemplate.specialAbilities,
            maxLife: Math.floor(baseStats.life * (1 + (Math.random() - 0.5) * variance)),
            str: Math.floor(baseStats.str * (1 + (Math.random() - 0.5) * variance)),
            int: Math.floor(baseStats.int * (1 + (Math.random() - 0.5) * variance)),
            agl: Math.floor(baseStats.agl * (1 + (Math.random() - 0.5) * variance)),
            hasSummonedAlly: true // Prevent summoned ally from summoning again
        };

        // Add random AGL bonus of 0-3 points
        ally.agl += Math.floor(Math.random() * 4);

        ally.life = ally.maxLife;
        ally.expReward = Math.floor((ally.maxLife + ally.str + ally.int + ally.agl) * gameState.currentFloor * 5.0);

        // Add the ally to the enemies array
        this.enemies.push(ally);

        // Mark the summoner as having summoned an ally
        summoner.hasSummonedAlly = true;

        // Log the summoning
        this.addToLog(`✨ ${summoner.name} calls forth another Will-o'-the-wisp! ✨`, 'system');
        this.addToLog(`A ${ally.name} materializes to join the battle!`, 'system');

        // Refresh turn order to include the new ally
        this.setupTurnOrder();

        // Update combat UI to show the new enemy
        this.updateCombatUI();

        // Play enemy spawn sound effect if available
        if (window.soundManager) {
            soundManager.playCombatSound('enemy_defeat'); // Reuse existing sound for now
        }

        this.nextTurn();
    }
    
    // Move to next turn
    nextTurn() {
        // Check for zombie revival before checking if all enemies are defeated
        this.checkZombieRevival();

        // Check if all enemies are defeated
        const aliveEnemies = this.enemies.filter(enemy => enemy.life > 0);
        if (aliveEnemies.length === 0) {
            this.addToLog('All enemies are defeated!', 'system');
            // Play enemy defeat sound effect
            if (window.soundManager) {
                soundManager.playCombatSound('enemy_defeat');
            }
            gameState.incrementEnemiesDefeated();

            // Award experience from all enemies
            if (window.characterSystem) {
                const totalExp = this.enemies.reduce((sum, enemy) => sum + enemy.expReward, 0);
                characterSystem.awardExp(totalExp);
            }

            // Award gold based on floor (but not for beast monsters)
            const hasBeastMonsters = this.enemies.some(enemy =>
                enemy.life <= 0 && enemy.properties && enemy.properties.includes('beast')
            );

            if (!hasBeastMonsters) {
                const baseGold = 5 + Math.floor(Math.random() * 71); // 5-75 gold
                const floorMultiplier = Math.pow(1.3, gameState.currentFloor - 1); // 30% increase per floor
                const goldReward = Math.floor(baseGold * floorMultiplier); // Apply floor multiplier

                gameState.addGold(goldReward); // Add gold to party
                this.addToLog(`Party earned ${goldReward} Gold!`, 'system'); // Log gold reward
            }

            // Check for mapping tool drop
            this.checkMappingToolDrop();

            // Check for item drops
            this.checkItemDrop();

            // Clear enemy from dungeon
            gameState.getCurrentCell().enemy = false;

            // Check for boss victory
            if (gameState.getCurrentCell().type === 'boss') {
                document.getElementById('victoryScreen').classList.add('active');
            }

            setTimeout(() => this.endCombat(true), 1500);
            return;
        }
        
        const alivePartyMembers = gameState.getAlivePartyMembers();
        if (alivePartyMembers.length === 0) {
            this.addToLog('Your party has been defeated...', 'system');
            setTimeout(() => this.endCombat(false), 1500);
            return;
        }
        
        // Move to next turn
        this.currentTurnIndex = (this.currentTurnIndex + 1) % this.turnOrder.length;

        // Skip dead party members, dead enemies, paralyzed party members, and incapacitated party members
        let skipCount = 0;
        while ((this.turnOrder[this.currentTurnIndex].type === 'party' &&
                (this.turnOrder[this.currentTurnIndex].member.life <= 0 ||
                 characterSystem.isCharacterParalyzed(this.turnOrder[this.currentTurnIndex].member) ||
                 characterSystem.isIncapacitated(this.turnOrder[this.currentTurnIndex].member))) ||
               (this.turnOrder[this.currentTurnIndex].type === 'enemy' &&
                this.turnOrder[this.currentTurnIndex].member.life <= 0)) {

            // If it's a paralyzed party member, show paralysis message
            if (this.turnOrder[this.currentTurnIndex].type === 'party' &&
                this.turnOrder[this.currentTurnIndex].member.life > 0 &&
                characterSystem.isCharacterParalyzed(this.turnOrder[this.currentTurnIndex].member)) {
                this.addToLog(`⚡ ${this.turnOrder[this.currentTurnIndex].member.name} is paralyzed and cannot act! ⚡`, 'system');
            }

            // If it's an incapacitated party member, show incapacitation message
            if (this.turnOrder[this.currentTurnIndex].type === 'party' &&
                this.turnOrder[this.currentTurnIndex].member.life > 0 &&
                characterSystem.isIncapacitated(this.turnOrder[this.currentTurnIndex].member)) {
                this.addToLog(`💫 ${this.turnOrder[this.currentTurnIndex].member.name} is incapacitated and cannot act! 💫`, 'system');
            }

            this.currentTurnIndex = (this.currentTurnIndex + 1) % this.turnOrder.length;
            skipCount++;

            // Prevent infinite loop - if we've checked all turns, something is wrong
            if (skipCount >= this.turnOrder.length) {
                // Force end combat if all party members are dead
                const alivePartyMembers = gameState.getAlivePartyMembers();
                if (alivePartyMembers.length === 0) {
                    this.endCombat(false);
                    return;
                }
                // Otherwise, break the loop and continue with current turn
                break;
            }
        }
        
        // Update skill durations if it's a party member's turn
        const currentActor = this.turnOrder[this.currentTurnIndex];
        if (currentActor.type === 'party') {
            const skillUpdate = characterSystem.updateSkillDurations(currentActor.member);

            if (skillUpdate.shieldExpired) {
                this.addToLog(`🛡️ ${skillUpdate.shieldBearer}'s SHIELD effect has ended!`, 'system');
            }


        }

        // Process status effects at the end of each turn
        const statusUpdate = characterSystem.processStatusEffects();

        // Report silence expiration
        if (statusUpdate.silenceExpired.length > 0) {
            statusUpdate.silenceExpired.forEach(enemyName => {
                this.addToLog(`🔇 ${enemyName}'s SILENCE effect has ended!`, 'system');
            });
        }

        // Report incapacitation expiration
        if (statusUpdate.incapacitationExpired.length > 0) {
            statusUpdate.incapacitationExpired.forEach(memberName => {
                this.addToLog(`💫 ${memberName} has recovered from incapacitation! 💫`, 'system');
            });
        }

        // Report hide expiration
        if (statusUpdate.hideExpired.length > 0) {
            statusUpdate.hideExpired.forEach(memberName => {
                this.addToLog(`👤 ${memberName} is no longer hidden!`, 'system');
            });
        }

        // Report paralysis expiration
        if (statusUpdate.paralysisExpired.length > 0) {
            statusUpdate.paralysisExpired.forEach(memberName => {
                this.addToLog(`⚡ ${memberName} is no longer paralyzed!`, 'system');
            });
        }

        // Report protection expiration
        if (statusUpdate.protectionExpired.length > 0) {
            statusUpdate.protectionExpired.forEach(memberName => {
                this.addToLog(`🛡️ ${memberName}'s PROTECT effect has ended!`, 'system');
            });
        }

        // Report swift movement expiration
        if (statusUpdate.swiftMovementExpired.length > 0) {
            statusUpdate.swiftMovementExpired.forEach(memberName => {
                this.addToLog(`💨 ${memberName}'s SWIFT MOVEMENT effect has ended!`, 'system');
            });
        }

        // Process poison damage for all party members at the start of each turn
        gameState.party.forEach(member => {
            if (member.life > 0 && window.characterSystem) {
                const poisonResult = characterSystem.processPoisonDamageCombat(member);
                if (poisonResult) {
                    // Poison damage messages are suppressed - damage still applies but no visual notification
                    // this.addToLog(poisonResult.message, 'system');
                    if (poisonResult.cured) {
                        this.addToLog(`✨ ${member.name}'s poison has worn off! ✨`, 'system');
                    }
                }
            }
        });
        
        gameState.incrementTurn();
        
        // Handle enemy turn
        if (this.turnOrder[this.currentTurnIndex].type === 'enemy') {
            this.enemyTurn();
        }

        this.updateCombatUI();
    }
    
    // End combat
    endCombat(victory) {
        // Apply post-combat healing if victory
        if (victory) {
            this.applyPostCombatHealing();
        }

        // Stop combat music and restore previous music
        if (window.soundManager && soundManager.stopCombatMusic) {
            soundManager.stopCombatMusic().then(result => {
                if (result.success) {
                    console.log('Combat music stopped and previous music restored');
                } else {
                    console.warn('Failed to stop combat music:', result.error);
                }
            }).catch(error => {
                console.error('Error stopping combat music:', error);
            });
        }

        gameState.setCombatState(false);
        document.getElementById('combatUI').classList.remove('active');

        if (!victory && gameState.isGameOver()) {
            setTimeout(() => {
                document.getElementById('gameOver').classList.add('active');
            }, 500);
        }
    }
    
    // Apply post-combat healing to alive party members
    applyPostCombatHealing() {
        const aliveMembers = gameState.getAlivePartyMembers();
        const healedMembers = [];

        // Check if wild meat was obtained for enhanced healing
        const enhancedHealing = this.wildMeatObtained || false;

        aliveMembers.forEach(member => {
            // Only heal if member is not at full health
            if (member.life < member.maxLife) {
                let healingRate;

                if (enhancedHealing) {
                    // Enhanced healing from wild meat - up to 35%
                    healingRate = CONSTANTS.POST_COMBAT.ENHANCED_HEALING_RATE;
                } else {
                    // Normal healing - 10-15%
                    healingRate = CONSTANTS.POST_COMBAT.HEALING_RATE_MIN +
                        Math.random() * (CONSTANTS.POST_COMBAT.HEALING_RATE_MAX - CONSTANTS.POST_COMBAT.HEALING_RATE_MIN);
                }

                const healingAmount = Math.floor(member.maxLife * healingRate);

                const oldLife = member.life;
                member.life = Math.min(member.maxLife, member.life + healingAmount);
                const actualHealing = member.life - oldLife;

                if (actualHealing > 0) {
                    healedMembers.push({ name: member.name, amount: actualHealing });
                }
            }
        });
        
        // Add healing message to combat log if any members were healed
        if (healedMembers.length > 0) {
            this.addToLog('The party recovers slightly from the victory!', 'system');
            healedMembers.forEach(healed => {
                this.addToLog(`${healed.name} recovers ${healed.amount} LIFE`, 'system');
            });
        }
        
        // Update game state to reflect healing
        gameState.dispatchEvent(CONSTANTS.EVENTS.GAME_STATE_CHANGED);
    }
    
    // Update combat UI
    updateCombatUI() {
        // Update enemy display in left and right areas
        this.updateEnemyDisplay();
        
        // Update party status display
        this.updateCombatPartyInfo();
        
        // Update current turn indicator
        this.updateCurrentTurn();
        
        this.updateCombatLog();
    }
    
    // Update enemy display in separate left and right areas
    updateEnemyDisplay() {
        const leftArea = document.getElementById('enemyAreaLeft');
        const rightArea = document.getElementById('enemyAreaRight');
        
        if (!leftArea || !rightArea) return;
        
        // Clear existing content
        leftArea.innerHTML = '';
        rightArea.innerHTML = '';
        
        // Filter alive enemies
        const aliveEnemies = this.enemies.filter(enemy => enemy.life > 0);
        
        // Place enemies based on count and position
        aliveEnemies.forEach((enemy, index) => {
            const enemyCard = this.createEnemyCard(enemy);
            
            if (aliveEnemies.length === 1) {
                // Single enemy goes to left area
                leftArea.appendChild(enemyCard);
            } else {
                // Multiple enemies: first to left, second to right
                if (index === 0) {
                    leftArea.appendChild(enemyCard);
                } else if (index === 1) {
                    rightArea.appendChild(enemyCard);
                }
            }
        });
    }
    
    // Create enemy card element
    createEnemyCard(enemy) {
        const enemyCard = document.createElement('div');
        const isAlive = enemy.life > 0;
        const healthPercent = enemy.maxLife > 0 ? (enemy.life / enemy.maxLife) * 100 : 0;
        
        enemyCard.className = 'enemy-card';
        
        // Create image element with enhanced fallback using ImageManager
        let imageHtml;
        if (enemy.imagePath && window.imageManager && imageManager.isImageAvailable(enemy.imagePath)) {
            // Image is preloaded and available
            imageHtml = `<div class="enemy-image-container">
                <img src="${enemy.imagePath}" alt="${enemy.name}" class="enemy-image">
            </div>`;
        } else if (enemy.imagePath) {
            // Image path exists but not loaded, try with fallback
            imageHtml = `<div class="enemy-image-container">
                <img src="${enemy.imagePath}" alt="${enemy.name}" class="enemy-image"
                     onerror="this.style.display='none'; this.nextElementSibling.style.display='block'; console.warn('Failed to load enemy image: ${enemy.imagePath}');"
                     onload="this.style.display='block'; this.nextElementSibling.style.display='none';">
                <div class="enemy-image-fallback" style="display: none;">
                    <div style="font-size: 48px; color: #ff6666; text-shadow: 0 0 10px #ff6666;">👹</div>
                    <div style="font-size: 12px; color: #ff6666; margin-top: 5px;">${enemy.name}</div>
                </div>
            </div>`;
        } else {
            // No image path, use fallback
            imageHtml = `<div class="enemy-image-container">
                <div class="enemy-image-fallback">
                    <div style="font-size: 48px; color: #ff6666; text-shadow: 0 0 10px #ff6666;">👹</div>
                    <div style="font-size: 12px; color: #ff6666; margin-top: 5px;">${enemy.name}</div>
                </div>
            </div>`;
        }
        
        // Add status effect indicators for enemies
        let enemyStatusEffects = '';
        if (enemy.silenced && enemy.silenceTurnsLeft > 0) {
            enemyStatusEffects += ' <span style="color: #FF6B6B;">🔇</span>';
        }

        enemyCard.innerHTML = `
            ${imageHtml}
            <div class="enemy-info">
                <div class="enemy-name">${enemy.name}${enemyStatusEffects}</div>
                <div class="health-bar">
                    <div class="health-fill" style="width: ${healthPercent}%"></div>
                </div>
                <div class="enemy-stats">LIFE: ${enemy.life}/${enemy.maxLife}</div>
                <div class="enemy-stats">STR: ${enemy.str} | INT: ${enemy.int} | AGL: ${enemy.agl}</div>
                <div class="enemy-status ${isAlive ? 'alive' : 'defeated'}">${isAlive ? 'ALIVE' : 'DEFEATED'}</div>
            </div>
        `;
        
        return enemyCard;
    }
    
    // Update combat party info display
    updateCombatPartyInfo() {
        const partyContainer = document.getElementById('combatPartyMembers');
        if (!partyContainer) return;
        
        // Clear existing content
        partyContainer.innerHTML = '';
        
        // Get current turn member for highlighting
        const currentActor = this.turnOrder[this.currentTurnIndex];
        const currentTurnMember = currentActor && currentActor.type === 'party' ? currentActor.member : null;
        
        // Generate party member cards
        gameState.party.forEach((member, index) => {
            const partyCard = document.createElement('div');
            const isCurrentTurn = currentTurnMember && member.name === currentTurnMember.name;
            const isDead = member.life <= 0;
            
            partyCard.className = `combat-party-card ${isCurrentTurn ? 'current-turn' : ''} ${isDead ? 'dead' : ''}`;
            
            const healthPercent = member.maxLife > 0 ? (member.life / member.maxLife) * 100 : 0;
            
            // Determine status
            let status = '';
            if (isDead) {
                status = 'DEAD';
            } else if (member.defending) {
                status = 'DEFENDING';
            } else if (isCurrentTurn) {
                status = 'TURN';
            } else {
                status = 'READY';
            }

            // Add status effect indicators
            let statusEffects = '';
            if (member.poisoned && member.poisonTurnsLeft > 0) {
                statusEffects += ' <span style="color: #66ff66;">💚</span>';
            }
            if (member.paralyzed && member.paralysisTurnsLeft > 0) {
                statusEffects += ' <span style="color: #FFD700;">⚡</span>';
            }
            if (member.hidden && member.hiddenTurnsLeft > 0) {
                statusEffects += ' <span style="color: #888888;">👤</span>';
            }
            if (member.shieldActive && member.shieldTurnsLeft > 0) {
                statusEffects += ' <span style="color: #4169E1;">🛡️</span>';
            }
            if (member.protected && member.protectedTurnsLeft > 0) {
                statusEffects += ' <span style="color: #87CEEB;">🛡️</span>';
            }
            if (member.swiftMovement && member.swiftMovementTurnsLeft > 0) {
                statusEffects += ' <span style="color: #00FF7F;">💨</span>';
            }

            
            partyCard.innerHTML = `
                <div class="combat-party-name">${member.name}${statusEffects}</div>
                <div class="combat-party-level">Lv.${member.level}</div>
                <div class="combat-party-health">
                    <div class="combat-party-health-fill" style="width: ${healthPercent}%"></div>
                </div>
                <div class="combat-party-stats">${member.life}/${member.maxLife}</div>
                <div class="combat-party-stats">S:${member.str} I:${member.int} A:${member.agl}</div>
                <div class="combat-party-status ${member.defending ? 'defending' : ''} ${isDead ? 'dead' : ''}">${status}</div>
            `;
            
            partyContainer.appendChild(partyCard);
        });
    }
    
    // Update current turn indicator
    updateCurrentTurn() {
        const currentActor = this.turnOrder[this.currentTurnIndex];
        const turnMemberElement = document.getElementById('currentTurnMember');
        const turnStatsElement = document.getElementById('turnMemberStats');
        const turnInfoElement = document.getElementById('currentTurnInfo');
        
        if (currentActor.type === 'party') {
            const member = currentActor.member;
            turnMemberElement.textContent = `${member.name} (Lv.${member.level})`;
            turnMemberElement.className = 'turn-member';
            turnStatsElement.textContent = 
                `LIFE: ${member.life}/${member.maxLife} | STR: ${member.str} | INT: ${member.int} | AGL: ${member.agl}`;
            
            turnInfoElement.style.animationName = 'turn-glow';
        } else {
            turnMemberElement.textContent = 'Enemy Turn';
            turnMemberElement.className = 'turn-member enemy-turn';
            
            if (this.waitingForPlayerInput) {
                turnStatsElement.textContent = 'Press Enter or Click to continue...';
            } else {
                turnStatsElement.textContent = 'Enemy is thinking...';
            }
            
            turnInfoElement.style.animationName = 'enemy-turn-glow';
        }
    }
    
    // Update combat log
    updateCombatLog() {
        const logDiv = document.getElementById('combatLog');
        logDiv.innerHTML = '';
        
        this.combatLog.slice(-10).forEach(entry => {
            const div = document.createElement('div');
            div.className = `log-entry log-${entry.type}`;
            div.textContent = entry.message;
            logDiv.appendChild(div);
        });
        
        logDiv.scrollTop = logDiv.scrollHeight;
    }
    
    // Add message to combat log
    addToLog(message, type = 'system') {
        this.combatLog.push({ message, type });
        this.updateCombatLog();
    }
    
    // Player action handler
    playerAction(action) {
        // Check if we have a valid turn order
        if (!this.turnOrder || this.turnOrder.length === 0) {
            return;
        }

        const currentActor = this.turnOrder[this.currentTurnIndex];

        // If we're waiting for player input during enemy turn, allow skipping
        if (this.waitingForPlayerInput && currentActor.type === 'enemy') {
            this.skipEnemyThinking();
            return;
        }

        // Make sure it's a party member's turn
        if (currentActor.type !== 'party') {
            return;
        }
        
        const actor = currentActor.member;
        
        // For attack and magic actions, randomly select a living enemy
        let target = null;
        if (action === 'attack' || action === 'magic') {
            const aliveEnemies = this.enemies.filter(enemy => enemy.life > 0);
            if (aliveEnemies.length > 0) {
                target = aliveEnemies[Math.floor(Math.random() * aliveEnemies.length)];
                this.addToLog(`${actor.name} targets ${target.name}!`, 'system');
            }
        } else if (action === 'flee') {
            // For flee, use the strongest enemy
            target = this.enemies.reduce((strongest, enemy) => 
                enemy.life > 0 && enemy.agl > strongest.agl ? enemy : strongest
            );
        } else if (action === 'concentrate' || action === 'defend') {
            // These actions don't require a target
            target = null;
        } else if (action === 'skill') {
            // Show skill selection menu instead of auto-executing
            if (actor.concentrated && characterSystem.canUseSkills(actor)) {
                this.showSkillSelection(actor);
                return; // Don't continue with action execution
            } else {
                target = null;
            }
        }
        
        // Execute the action
        if (action === 'concentrate' || action === 'defend') {
            // Actions that don't need a target
            this.executeAction(actor, null, action);
        } else if (action === 'skill') {
            // Skill action (may or may not need target)
            this.executeAction(actor, target, action);
        } else if (target) {
            this.executeAction(actor, target, action);
        }
        
        // Move to next turn
        this.nextTurn();
    }
    
    // Check for mapping tool drop after enemy defeat
    checkMappingToolDrop() {
        // Only check if mapping tool hasn't been obtained yet
        if (!gameState.hasMappingTool) {
            // 3% chance to obtain mapping tool
            if (Math.random() < CONSTANTS.MAPPING_TOOL.DROP_CHANCE) {
                gameState.obtainMappingTool();
                this.addToLog('Mapping Tool を取得しました！ミニマップが利用可能になりました。', 'system');
            }
        }
    }
    
    // Check for zombie revival
    checkZombieRevival() {
        this.enemies.forEach(enemy => {
            // Check if this is a dead zombie that hasn't been revived yet
            if (enemy.life <= 0 &&
                enemy.type === 'Zombie' &&
                !enemy.hasRevived &&
                enemy.specialAbilities &&
                enemy.specialAbilities.reviveChance) {

                // Roll for revival chance
                if (Math.random() < enemy.specialAbilities.reviveChance) {
                    // Revive the zombie with partial health
                    const reviveHealth = Math.floor(enemy.maxLife * 0.5); // 50% of max health
                    enemy.life = reviveHealth;
                    enemy.hasRevived = true; // Prevent multiple revivals

                    this.addToLog(`🧟 ${enemy.name} rises from the dead with ${reviveHealth} LIFE! 🧟`, 'system');

                    // Play a revival sound effect if available
                    if (window.soundManager) {
                        soundManager.playEnemyDefeat(); // Reuse existing sound for now
                    }

                    // Update the turn order to include the revived zombie
                    this.setupTurnOrder();
                }
            }
        });
    }

    // Check for item drops after enemy defeat
    checkItemDrop() {
        // Check for ORB drops first (special drop from magical creatures and dragons)
        this.checkOrbDrop();

        // Check for MYSTIC_ORB drops (special drop from otherworld creatures)
        this.checkMysticOrbDrop();

        // Check for wild meat drops from beast monsters
        this.checkWildMeatDrop();

        // Check if any defeated enemy is a beast monster
        const hasBeastMonsters = this.enemies.some(enemy =>
            enemy.life <= 0 && enemy.properties && enemy.properties.includes('beast')
        );

        // Only drop regular items if no beast monsters were defeated
        if (!hasBeastMonsters && Math.random() < CONSTANTS.INVENTORY.COMBAT_DROP_CHANCE) {
            // Currently only Health Potions drop
            const itemId = 'health_potion';
            const quantity = 1;

            // Try to add the item to inventory
            if (gameState.addItem(itemId, quantity)) {
                const itemData = CONSTANTS.ITEMS.HEALTH_POTION;
                this.addToLog(`Found ${itemData.name}!`, 'system');
            } else {
                // Inventory is full
                this.addToLog('An item was found but your inventory is full!', 'system');
            }
        }
    }

    // Check for ORB drops from magical creatures and dragons
    checkOrbDrop() {
        // Check if any defeated enemy can drop an ORB
        let canDropOrb = false;
        let dropChance = 0;

        for (const enemy of this.enemies) {
            if (enemy.life <= 0) { // Only check defeated enemies
                if (enemy.properties && enemy.properties.includes('magical_creature')) {
                    canDropOrb = true;
                    dropChance = Math.max(dropChance, 0.03); // 3% chance for magical creatures
                } else if (enemy.properties && enemy.properties.includes('dragon')) {
                    canDropOrb = true;
                    dropChance = Math.max(dropChance, 0.05); // 5% chance for dragons
                }
            }
        }

        // Only one ORB can drop per combat encounter
        if (canDropOrb && Math.random() < dropChance) {
            const itemId = 'orb';
            const quantity = 1;

            // Try to add the ORB to inventory
            if (gameState.addItem(itemId, quantity)) {
                const itemData = CONSTANTS.ITEMS.ORB;
                this.addToLog(`Found ${itemData.name}!`, 'system');
            } else {
                // Inventory is full
                this.addToLog('An orb was found but your inventory is full!', 'system');
            }
        }
    }

    // Check for MYSTIC_ORB drops from otherworld creatures
    checkMysticOrbDrop() {
        // Check if any defeated enemy can drop a MYSTIC_ORB
        let canDropMysticOrb = false;
        let dropChance = 0;

        for (const enemy of this.enemies) {
            if (enemy.life <= 0) { // Only check defeated enemies
                if (enemy.properties && enemy.properties.includes('otherworld')) {
                    canDropMysticOrb = true;
                    dropChance = Math.max(dropChance, 0.03); // 3% chance for otherworld creatures
                }
            }
        }

        // Only one MYSTIC_ORB can drop per combat encounter
        if (canDropMysticOrb && Math.random() < dropChance) {
            const itemId = 'mystic_orb';
            const quantity = 1;

            // Try to add the MYSTIC_ORB to inventory
            if (gameState.addItem(itemId, quantity)) {
                const itemData = CONSTANTS.ITEMS.MYSTIC_ORB;
                this.addToLog(`Found ${itemData.name}!`, 'system');
            } else {
                // Inventory is full
                this.addToLog('A mystic orb was found but your inventory is full!', 'system');
            }
        }
    }

    // Check for wild meat drops from beast monsters
    checkWildMeatDrop() {
        // Check if any defeated enemy is a beast monster
        let canDropWildMeat = false;

        for (const enemy of this.enemies) {
            if (enemy.life <= 0 && enemy.properties && enemy.properties.includes('beast')) {
                canDropWildMeat = true;
                break;
            }
        }

        // 40% chance to obtain wild meat from beast monsters
        if (canDropWildMeat && Math.random() < CONSTANTS.BEAST_MONSTERS.WILD_MEAT_DROP_CHANCE) {
            // Wild meat is auto-consumed and enhances post-combat healing
            this.wildMeatObtained = true;
            this.addToLog('Wild meat obtained! Enhanced healing will be applied.', 'system');

            // Show wild meat notification if available
            if (typeof wildMeatNotification !== 'undefined') {
                setTimeout(() => {
                    wildMeatNotification.showNotification();
                }, 500);
            }
        }
    }

    // Show skill selection menu
    showSkillSelection(actor) {
        const skillSelectionUI = document.getElementById('skillSelectionUI');
        const skillSelectionList = document.getElementById('skillSelectionList');
        const skillSelectionDescription = document.getElementById('skillSelectionDescription');

        if (!skillSelectionUI || !skillSelectionList || !skillSelectionDescription) {
            console.error('Skill selection UI elements not found');
            return;
        }

        // Store current actor for skill execution
        this.currentSkillActor = actor;

        // Update description
        skillSelectionDescription.textContent = `${actor.name} - Choose a skill to use:`;

        // Clear previous skills
        skillSelectionList.innerHTML = '';

        // Get available skills for the character
        const availableSkills = characterSystem.getAvailableSkills(actor);

        if (availableSkills.length === 0) {
            const noSkillsDiv = document.createElement('div');
            noSkillsDiv.className = 'skill-option disabled';
            noSkillsDiv.innerHTML = `
                <div class="skill-name">No Skills Available</div>
                <div class="skill-description">This character has not learned any skills yet.</div>
            `;
            skillSelectionList.appendChild(noSkillsDiv);
        } else {
            // Create skill option buttons
            availableSkills.forEach(skill => {
                const skillDiv = document.createElement('div');
                skillDiv.className = 'skill-option';
                skillDiv.onclick = () => this.selectSkill(skill.id);

                skillDiv.innerHTML = `
                    <div class="skill-name">${skill.name}</div>
                    <div class="skill-description">${skill.description}</div>
                    <div class="skill-level">Level ${skill.level} Skill</div>
                `;

                skillSelectionList.appendChild(skillDiv);
            });
        }

        // Show the skill selection UI
        skillSelectionUI.style.display = 'block';
    }

    // Select a skill from the menu
    selectSkill(skillId) {
        const actor = this.currentSkillActor;
        if (!actor) {
            console.error('No actor stored for skill selection');
            return;
        }

        // Hide skill selection UI
        this.cancelSkillSelection();

        // Get skill data
        const availableSkills = characterSystem.getAvailableSkills(actor);
        const selectedSkill = availableSkills.find(skill => skill.id === skillId);

        if (!selectedSkill) {
            console.error(`Skill ${skillId} not found in available skills`);
            return;
        }

        // Handle skill execution based on targeting requirements
        if (selectedSkill.requiresTarget) {
            this.handleSkillTargeting(actor, selectedSkill);
        } else {
            // Execute skill without target
            this.executeSkillAction(actor, selectedSkill, null);
        }
    }

    // Handle targeting for skills that require targets
    handleSkillTargeting(actor, skill) {
        if (skill.targetType === 'ally' && skill.id === 'first_aid') {
            // Show target selection UI for First Aid
            this.showTargetSelection(actor, skill);
        } else if (skill.targetType === 'ally') {
            // Auto-select random alive party member for other ally skills
            const alivePartyMembers = gameState.getAlivePartyMembers();
            if (alivePartyMembers.length > 0) {
                const target = alivePartyMembers[Math.floor(Math.random() * alivePartyMembers.length)];
                this.addToLog(`${actor.name} targets ${target.name} for ${skill.name}!`, 'system');
                this.executeSkillAction(actor, skill, target);
            }
        } else if (skill.targetType === 'enemy') {
            // Auto-select random alive enemy for attack skills
            const aliveEnemies = this.enemies.filter(enemy => enemy.life > 0);
            if (aliveEnemies.length > 0) {
                const target = aliveEnemies[Math.floor(Math.random() * aliveEnemies.length)];
                this.addToLog(`${actor.name} targets ${target.name} for ${skill.name}!`, 'system');
                this.executeSkillAction(actor, skill, target);
            }
        }
    }

    // Execute the selected skill
    executeSkillAction(actor, skill, target) {
        // Store the selected skill for use in executeAction
        this.currentSelectedSkill = skill;

        // Use the existing skill execution logic
        this.executeAction(actor, target, 'skill');

        // Clear the selected skill after execution
        this.currentSelectedSkill = null;

        // Move to next turn after skill execution
        this.nextTurn();
    }

    // Cancel skill selection and return to combat menu
    cancelSkillSelection() {
        const skillSelectionUI = document.getElementById('skillSelectionUI');
        if (skillSelectionUI) {
            skillSelectionUI.style.display = 'none';
        }
        this.currentSkillActor = null;
    }

    // Show target selection menu for First Aid
    showTargetSelection(actor, skill) {
        const targetSelectionUI = document.getElementById('targetSelectionUI');
        const targetSelectionList = document.getElementById('targetSelectionList');
        const targetSelectionDescription = document.getElementById('targetSelectionDescription');

        if (!targetSelectionUI || !targetSelectionList || !targetSelectionDescription) {
            console.error('Target selection UI elements not found');
            return;
        }

        // Store current actor and skill for target selection
        this.currentSkillActor = actor;
        this.currentSelectedSkill = skill;

        // Update description
        targetSelectionDescription.textContent = `${actor.name} - Choose a party member to heal with ${skill.name}:`;

        // Clear previous targets
        targetSelectionList.innerHTML = '';

        // Get all party members (alive and dead for First Aid)
        gameState.party.forEach(member => {
            const targetDiv = document.createElement('div');
            const isDead = member.life <= 0;
            
            targetDiv.className = `target-option ${isDead ? 'dead' : ''}`;
            targetDiv.onclick = () => this.selectTarget(member);

            const healthPercent = member.maxLife > 0 ? (member.life / member.maxLife) * 100 : 0;

            targetDiv.innerHTML = `
                <div class="target-name">${member.name} (Lv.${member.level})</div>
                <div class="target-stats">STR: ${member.str} | INT: ${member.int} | AGL: ${member.agl}</div>
                <div class="target-health">LIFE: ${member.life}/${member.maxLife} (${Math.round(healthPercent)}%)</div>
            `;

            targetSelectionList.appendChild(targetDiv);
        });

        // Show the target selection UI
        targetSelectionUI.style.display = 'block';
    }

    // Select a target from the menu
    selectTarget(target) {
        const actor = this.currentSkillActor;
        const skill = this.currentSelectedSkill;
        
        if (!actor || !skill) {
            console.error('No actor or skill stored for target selection');
            return;
        }

        // Hide target selection UI
        this.cancelTargetSelection();

        // Add log message about target selection
        this.addToLog(`${actor.name} targets ${target.name} for ${skill.name}!`, 'system');

        // Execute the skill with the selected target
        this.executeSkillAction(actor, skill, target);
    }

    // Cancel target selection and return to combat menu
    cancelTargetSelection() {
        const targetSelectionUI = document.getElementById('targetSelectionUI');
        if (targetSelectionUI) {
            targetSelectionUI.style.display = 'none';
        }
        this.currentSkillActor = null;
        this.currentSelectedSkill = null;
    }
}

// Create global instance
const combatSystem = new Combat();

// Make available globally for browser
if (typeof window !== 'undefined') {
    window.combatSystem = combatSystem;
}
